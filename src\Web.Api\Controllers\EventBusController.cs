using Api.Filter;
using BPM.Events.Messages;
using BPM.Extras.Youzan.Request;
using BPM.Extras.Youzan.Responses;
using BPM.Extras.Youzan.Services;
using Domain.order.Repository;
using Domain.Shared;
using DotNetCore.CAP;
using Infrastructure.UnitOfWorks;
using System.Collections.Generic;
using Web.Api.EventHandlers.Youzan;
using static BPM.Extras.Youzan.Request.orderRefundRequest;
using static BPM.Extras.Youzan.Request.orderTradeRequest;
using static Web.Api.EventHandlers.Youzan.orderRefundMessageEvent;
using static Web.Api.EventHandlers.Youzan.orderTradeMessageEvent;

/// <summary>
/// EventBus控制器
/// </summary>
[Route("api/eventBus")]
public class EventBusController : ApiControllerBase
{

    /// <summary>
    /// 工作单元
    /// </summary>
    public IBaseUnitOfWork UnitOfWork { get; set; }

    /// <summary>
    /// 消息事件总线
    /// </summary>
    public IMessageEventBus MessageEventBus { get; }


    /// <summary>
    /// 商品应用接口
    /// </summary>
    private readonly IProdcutAppService _prodcutAppService;

    /// <summary>
    /// 订单应用接口
    /// </summary>
    private readonly IOrderAppService _orderAppService;

    /// <summary>
    /// 门店应用接口
    /// </summary>
    private readonly IShopAppService _shopAppService;


    /// <summary>
    /// 有赞服务接口
    /// </summary>
    private readonly IYouzanService _youzanService;

    /// <summary>
    /// 初始化一个<see cref="TestController"/>类型的实例
    /// </summary>
    public EventBusController(IMessageEventBus messageEventBus
        , IBaseUnitOfWork baseUnitOfWork, IOrderAppService orderAppService
        , IProdcutAppService prodcutAppService, IShopAppService shopAppService
        , IYouzanService youzanService)
    {
        MessageEventBus = messageEventBus;
        UnitOfWork = baseUnitOfWork;
        _orderAppService = orderAppService;
        _prodcutAppService = prodcutAppService;
        _shopAppService = shopAppService;
        _youzanService = youzanService;
    }


    /// <summary>
    /// 测试消息
    /// </summary>
    /// <param name="request">请求</param>
    [AllowAnonymous, NoSignAttribute, HttpPost("eventBusMessage")]
    public async Task<IActionResult> TestMessageAsync([FromBody] tradeMessage request)
    {
        //var logs = new syncLogsEntity();
        //logs.SyncStatus = 200;
        //logs.Body = "333";
        //logs.SourceId = "1001032023032600209";
        //await _orderRepository.saveSyncLogs(logs);
        //await MessageEventBus.PublishAsync("orderEvent", "1009882023033000007", "", true);

        // await MessageEventBus.PublishAsync(new orderTradeMessageEvent(request));

        //await MessageEventBus.PublishAsync("Event", "1001032023032600209", "callbackOrderEvent", true);
        //await MessageEventBus.PublishAsync(new TestMessageEvent1(request));
        //await UnitOfWork.CommitAsync(); 
        var jsonData = new { number = request.order_sn };
        Log.Info($"订单同步，接到订单同步请求-->{request.order_sn}");

        var trade = new tradeMessage();
        trade.order_sn = request.order_sn;
        Log.Info($"订单同步，发布订单事件-->{request.order_sn}");
        await MessageEventBus.PublishAsync(new orderTradeMessageEvent(trade));

        return Success("");
    }

    /// <summary>
    ///  同步销售单到有赞
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [AllowAnonymous, NoSignAttribute, HttpPost("syncYuzanSaleOrder")]
    public async Task syncYuzanSaleOrder(tradeMessage message)
    {
        var request = new orderTradeRequest();
        var orderItems = await _orderAppService.getshopTxnsList(message.order_sn);
        // 订单主体
        request.main_info = new oderBaseInfo()
        {
            out_biz_no = message.order_sn,
            create_time = orderItems[0].TXNTIME,
            pay_time = orderItems[0].TXNTIME
        };
        // 订单原价
        var origin_price = orderItems.Where(x => x.TXNTYPE == 0 && x.SALESTYPE < 6).Sum(x => x.ORGAMT);
        //订单销售价
        var current_price = orderItems.Where(x => x.TXNTYPE == 0 && x.SALESTYPE < 6).Sum(x => x.ORGAMT + x.ITEMDISC + x.TTLDISC);
        // 订单价格
        request.order_price = new orderPrice()
        {
            current_price = current_price,
            origin_price = origin_price,
            total_price = current_price,
            promotion_amount = origin_price - current_price
        };
        // 买家信息
        request.buyer = new buyerInfo()
        {
        };
        var trade_items = new List<tradeItems>();
        foreach (var item in orderItems.Where(x => x.TXNTYPE == 0 && x.SALESTYPE < 6))
        {
            var product_info = await _prodcutAppService.getProductSkuInfo(item.CODE);
            if (!product_info.IsNull())
            {
                trade_items.Add(new tradeItems()
                {
                    // item_id = product_info.product_id,
                    num = item.QTY,
                    //sku_id = item.CODE,
                    origin_price = item.ORGAMT,
                    current_price = item.ORGAMT + item.ITEMDISC + item.TTLDISC,
                    current_total_amount = current_price,
                    goods_info = new tradeItems.goodsInfo()
                    {
                        sku_no = item.CODE,
                        title = product_info.sku_name
                    }
                });
            }
        }
        // 订单交易信息
        request.trade_items = trade_items;
        var shop_info = await _shopAppService.getShopInfoById(orderItems[0].SHOPID);
        if (!shop_info.IsNull())
        {
            request.seller = new sellerInfo()
            {
                kdt_id = shop_info.source_no.ToLong(),
                out_kdt_id = shop_info.store_no,
                shop_name = shop_info.store_name
            };
        }
        // 收件人
        request.receiver = new receiverInfo()
        {
            receiver_name = "线下门店",
            logistics_type = "NONE",

        };
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.scrm.order.trade.out.create/1.0.0";
        parameter.body = request;
        var response = await _youzanService.getYouzanData(parameter);
        // WriteLog("同步POS订单", parameter.url, parameter.body.ToJson(), response.ToJson());
        if (response.success)
        {
            var result = response.data.ToString().ToObject<orderTradeResponse>();
            // 同步积分
            var pointRequset = new customerPointRequest();
            pointRequset.reason = "线下退款";
            pointRequset.source_kdt_id = request.seller.kdt_id;
            pointRequset.points = orderItems.Where(x => x.TXNTYPE == 0 && x.SALESTYPE < 6).Sum(s => s.CPTS);
            //pointRequset.user = new userInfo()
            //{
            //    account_id = message.openId,
            //    account_type = 1
            //};
            //await syncPoint(pointRequset);
        }
    }

    /// <summary>
    /// 同步积分
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    // [AllowAnonymous, NoSignAttribute, HttpPost("syncYuzanSaleOrderPoint")]
    // public async Task syncYuzanSaleOrderPoint()
    // {
    //     var request = new customerPointRequest()
    //     {
    //         reason = "消费增加积分",
    //         points = 100,
    //         source_kdt_id = *********,
    //         user = new customerPointRequest.userInfo()
    //         {
    //              account_id="***********"
    //         }
    //     };
    //     var parameter = new YouzanParameter();
    //     parameter.url = $"/api/youzan.crm.customer.points.increase/4.0.0";
    //     var dict = new Dictionary<string, object>();
    //     dict.Add("params", request);
    //     parameter.body = dict;
    //     var response = await _youzanService.getYouzanData(parameter);
    //     if (response.success)
    //     {
    //     }
    // }



    /// <summary>
    /// 同步退款单到有赞
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    [AllowAnonymous, NoSignAttribute, HttpPost("syncYuzanRefundOrder")]
    public async Task syncYuzanRefundOrder(tradeMessage message)
    {

        var orderItems = await _orderAppService.getshopTxnsList(message.order_sn);
        // 订单主体
        // 订单原价
        var origin_price = orderItems.Where(x => x.TXNTYPE == 1 && x.SALESTYPE < 6).Sum(x => x.ORGAMT);
        //订单销售价
        var current_price = orderItems.Where(x => x.TXNTYPE == 1 && x.SALESTYPE < 6).Sum(x => x.ORGAMT + x.ITEMDISC + x.TTLDISC);

        var shop_info = await _shopAppService.getShopInfoById(orderItems[0].SHOPID);
        // 基础信息
        var request = new orderRefundRequest()
        {
            out_order_no = message.order_sn,
            out_order_time = orderItems[0].TXNTIME,
            out_order_price = current_price * -1,
            origin_kdt_id = shop_info.source_no.ToLong()
        };
        // 买家信息
        if (!orderItems[0].PHONE.IsEmpty())
        {
            request.user = new orderRefundRequest.userInfo()
            {
                account_id = orderItems[0].PHONE
            };
        }
        var trade_items = new List<refundItems>();
        foreach (var item in orderItems.Where(x => x.TXNTYPE == 1 && x.SALESTYPE < 6))
        {
            var product_info = await _prodcutAppService.getProductSkuInfo(item.CODE);
            if (!product_info.IsNull())
            {
                trade_items.Add(new refundItems()
                {
                    item_id = product_info.product_id,
                    // 数量
                    num = item.QTY * -1,
                    sku_id = product_info.id,
                    // 商品名称
                    title = product_info.sku_name,
                    out_order_item_id = item.TXNID,
                    // 实付金额
                    current_unit_price = (item.ORGAMT + item.ITEMDISC + item.TTLDISC) * -1,
                    // 商品原始金额
                    origin_unit_price = item.ORGAMT * -1,
                    // 实付总金额
                    current_total_price = (item.ORGAMT + item.ITEMDISC + item.TTLDISC) * item.QTY,
                    // 原始总金额
                    origin_total_price = item.ORGAMT * item.QTY
                });
            }
        }
        request.items = trade_items;
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.crm.out.order.create/2.0.0";
        parameter.body = request;
        var response = await _youzanService.getYouzanData(parameter);
        if (response.success)
        {
            var result = response.data.ToString().ToObject<orderTradeResponse>();
            // 同步积分
            var pointRequset = new customerPointRequest();
            pointRequset.reason = "线下退款";
            pointRequset.source_kdt_id = request.origin_kdt_id;
            pointRequset.points = orderItems.Where(x => x.TXNTYPE == 0 && x.SALESTYPE < 6).Sum(s => s.CPTS);
            pointRequset.user = new customerPointRequest.userInfo()
            {
            };
        }
    }


    /// <summary>
    /// 同步积分
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [AllowAnonymous, NoSignAttribute, HttpPost("syncYuzanRefundOrderPoint")]
    public async Task syncYuzanRefundOrderPoint()
    {
        var request = new customerPointRequest()
        {
            reason = "退款减积分",
            points = 90,
            source_kdt_id = *********,
            user = new customerPointRequest.userInfo()
            {
                account_id = "***********"
            }
        };
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.crm.customer.points.decrease/4.0.0";
        var dict = new Dictionary<string, object>();
        dict.Add("params", request);
        parameter.body = dict;
        var response = await _youzanService.getYouzanData(parameter);
        if (response.success)
        {
        }

    }

    static int i = 0;
    /// <summary>
    /// 测试接受消息
    /// </summary>
    /// <param name="request">请求</param>
    [NonAction]
    [CapSubscribe("Event")]
    public Task<string> TestMessageAsync1(string request)
    {
        i++;
        if (i < 3)
        {
            throw new Exception("i 小于3，抛出异常");
        }
        return Task.FromResult(request.ToJson());
    }

    /// <summary>
    /// 回调函数
    /// </summary>
    /// <param name="order">
    [NonAction]
    [CapSubscribe("callbackFunction")]
    public void MarkOrderStatus(string callback_name)
    {

    }
}


/// <summary>
/// 测试消息事件
/// </summary>
public class TestMessageEvent : MessageEvent<TestMessage>
{
    /// <summary>
    /// 初始化一个<see cref="TestMessageEvent1"/>类型的实例
    /// </summary>
    /// <param name="data">数据</param>
    public TestMessageEvent(TestMessage data) : base(data)
    {
        Send = false;
        Name = "Event";
    }
}

/// <summary>
/// 测试消息
/// </summary>
public class TestMessage
{
    /// <summary>
    /// 标识
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; }
}
